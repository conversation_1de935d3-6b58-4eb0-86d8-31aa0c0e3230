<?php

/**
 * 生成 Services/Api 目录检查总结报告
 */

// 读取详细报告
$reportFile = 'services_data_check_report.json';
if (!file_exists($reportFile)) {
    echo "错误: 找不到报告文件 {$reportFile}\n";
    echo "请先运行 check_services_data.php\n";
    exit(1);
}

$reportData = json_decode(file_get_contents($reportFile), true);

echo "=== Services/Api 目录 'services_data' 检查总结报告 ===\n\n";
echo "扫描时间: {$reportData['scan_time']}\n\n";

// 总体统计
$summary = $reportData['summary'];
echo "总体统计:\n";
echo "- 服务文件总数: {$summary['total_files']}\n";
echo "- public 方法总数: {$summary['total_methods']}\n";
echo "- 包含 'services_data' 的方法: {$summary['methods_with_services_data']}\n";
echo "- 缺少 'services_data' 的方法: {$summary['methods_without_services_data']}\n\n";

// 分析构造函数和非构造函数
$constructorMissing = 0;
$nonConstructorMissing = 0;
$totalConstructors = 0;
$totalNonConstructors = 0;

foreach ($reportData['missing_services_data'] as $item) {
    if ($item['is_constructor']) {
        $constructorMissing++;
    } else {
        $nonConstructorMissing++;
    }
}

foreach ($reportData['detailed_results'] as $fileName => $data) {
    $totalConstructors += count($data['constructor_methods']);
    $totalNonConstructors += count($data['non_constructor_methods']);
}

echo "方法类型分析:\n";
echo "- 构造函数总数: {$totalConstructors}\n";
echo "- 构造函数缺少 services_data: {$constructorMissing}\n";
echo "- 非构造函数总数: {$totalNonConstructors}\n";
echo "- 非构造函数缺少 services_data: {$nonConstructorMissing}\n\n";

// 重点关注：非构造函数缺少 services_data 的情况
if ($nonConstructorMissing > 0) {
    echo "⚠️  重要发现：有 {$nonConstructorMissing} 个非构造函数缺少 'services_data' => \$services_data\n\n";
    
    echo "非构造函数缺少 services_data 的详细列表:\n";
    foreach ($reportData['missing_services_data'] as $item) {
        if (!$item['is_constructor']) {
            echo "- {$item['file']} -> {$item['method']}()\n";
        }
    }
    echo "\n";
} else {
    echo "✅ 好消息：所有非构造函数都包含了 'services_data' => \$services_data\n\n";
}

// 构造函数情况说明
if ($constructorMissing > 0) {
    echo "📝 构造函数情况说明:\n";
    echo "发现 {$constructorMissing} 个构造函数缺少 'services_data' => \$services_data\n";
    echo "这通常是正常的，因为构造函数主要用于初始化，不一定需要返回 services_data\n\n";
    
    echo "缺少 services_data 的构造函数列表:\n";
    foreach ($reportData['missing_services_data'] as $item) {
        if ($item['is_constructor']) {
            echo "- {$item['file']}\n";
        }
    }
    echo "\n";
}

// 完全符合要求的服务文件
echo "✅ 完全符合要求的服务文件（所有非构造函数都包含 services_data）:\n";
$compliantFiles = [];
foreach ($reportData['detailed_results'] as $fileName => $data) {
    $hasNonConstructorMissing = false;
    foreach ($reportData['missing_services_data'] as $item) {
        if ($item['file'] === $fileName && !$item['is_constructor']) {
            $hasNonConstructorMissing = true;
            break;
        }
    }
    
    if (!$hasNonConstructorMissing && count($data['non_constructor_methods']) > 0) {
        $compliantFiles[] = $fileName;
        $methodCount = count($data['non_constructor_methods']);
        echo "- {$fileName} ({$methodCount} 个非构造函数方法)\n";
    }
}

if (empty($compliantFiles)) {
    echo "暂无完全符合要求的服务文件\n";
}

echo "\n=== 下一步任务方案 ===\n\n";

if ($nonConstructorMissing > 0) {
    echo "🎯 需要处理的任务:\n";
    echo "1. 检查并修复 {$nonConstructorMissing} 个缺少 'services_data' => \$services_data 的非构造函数方法\n";
    echo "2. 确保这些方法的返回值中包含正确的 services_data 结构\n\n";
    
    echo "具体需要修复的方法:\n";
    foreach ($reportData['missing_services_data'] as $item) {
        if (!$item['is_constructor']) {
            echo "- {$item['file']} -> {$item['method']}()\n";
        }
    }
} else {
    echo "🎉 恭喜！所有非构造函数方法都已包含 'services_data' => \$services_data\n";
    echo "✅ 当前代码库在这方面已经完全符合要求\n";
}

echo "\n📊 统计完成时间: " . date('Y-m-d H:i:s') . "\n";
