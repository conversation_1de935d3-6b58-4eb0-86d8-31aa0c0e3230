<?php

/**
 * 详细分析 Services/Api 目录中的服务文件
 * 专门检查非构造函数的 public 方法是否包含 'services_data' => $services_data
 */

class DetailedServicesAnalysis
{
    private $servicesDir;
    private $results = [];
    private $nonConstructorMissingMethods = [];
    
    public function __construct()
    {
        $this->servicesDir = __DIR__ . '/php/api/app/Services/Api';
    }
    
    public function analyze()
    {
        echo "=== 详细分析服务文件中的非构造函数方法 ===\n\n";
        
        $files = glob($this->servicesDir . '/*.php');
        
        foreach ($files as $file) {
            $this->analyzeFile($file);
        }
        
        $this->generateTaskPlan();
    }
    
    private function analyzeFile($filePath)
    {
        $fileName = basename($filePath);
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            return;
        }
        
        // 获取类名
        $className = $this->getClassName($content);
        
        // 查找所有 public 方法
        $pattern = '/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/';
        preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
        
        $nonConstructorMethods = [];
        $methodsWithServicesData = [];
        $methodsWithoutServicesData = [];
        
        foreach ($matches[1] as $index => $methodMatch) {
            $methodName = $methodMatch[0];
            $methodStartPos = $matches[0][$index][1];
            
            // 跳过构造函数
            if ($methodName === '__construct') {
                continue;
            }
            
            $nonConstructorMethods[] = $methodName;
            
            // 提取方法体
            $methodBody = $this->extractMethodBody($content, $methodStartPos);
            
            // 检查是否包含 'services_data' => $services_data
            if (strpos($methodBody, "'services_data' => \$services_data") !== false) {
                $methodsWithServicesData[] = $methodName;
            } else {
                $methodsWithoutServicesData[] = $methodName;
                $this->nonConstructorMissingMethods[] = [
                    'file' => $fileName,
                    'class' => $className,
                    'method' => $methodName
                ];
            }
        }
        
        // 只记录有非构造函数方法的文件
        if (!empty($nonConstructorMethods)) {
            $this->results[$fileName] = [
                'class' => $className,
                'non_constructor_methods' => $nonConstructorMethods,
                'methods_with_services_data' => $methodsWithServicesData,
                'methods_without_services_data' => $methodsWithoutServicesData
            ];
            
            echo "文件: {$fileName} (类: {$className})\n";
            echo "  非构造函数方法总数: " . count($nonConstructorMethods) . "\n";
            echo "  包含 services_data: " . count($methodsWithServicesData) . "\n";
            echo "  缺少 services_data: " . count($methodsWithoutServicesData) . "\n";
            
            if (!empty($methodsWithoutServicesData)) {
                echo "  缺少的方法: " . implode(', ', $methodsWithoutServicesData) . "\n";
            }
            echo "\n";
        }
    }
    
    private function getClassName($content)
    {
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            return $matches[1];
        }
        return 'Unknown';
    }
    
    private function extractMethodBody($content, $startPos)
    {
        $braceCount = 0;
        $inMethod = false;
        $methodBody = '';
        $length = strlen($content);
        
        for ($i = $startPos; $i < $length; $i++) {
            $char = $content[$i];
            
            if ($char === '{') {
                $braceCount++;
                $inMethod = true;
            } elseif ($char === '}') {
                $braceCount--;
            }
            
            if ($inMethod) {
                $methodBody .= $char;
            }
            
            if ($inMethod && $braceCount === 0) {
                break;
            }
        }
        
        return $methodBody;
    }
    
    private function generateTaskPlan()
    {
        echo "=== 下一步任务方案 ===\n\n";
        
        $totalFiles = count($this->results);
        $totalNonConstructorMethods = array_sum(array_map(function($data) {
            return count($data['non_constructor_methods']);
        }, $this->results));
        
        $totalWithServicesData = array_sum(array_map(function($data) {
            return count($data['methods_with_services_data']);
        }, $this->results));
        
        $totalWithoutServicesData = count($this->nonConstructorMissingMethods);
        
        echo "统计摘要:\n";
        echo "- 包含非构造函数方法的服务文件: {$totalFiles} 个\n";
        echo "- 非构造函数方法总数: {$totalNonConstructorMethods} 个\n";
        echo "- 已包含 'services_data' => \$services_data: {$totalWithServicesData} 个\n";
        echo "- 缺少 'services_data' => \$services_data: {$totalWithoutServicesData} 个\n\n";
        
        if (!empty($this->nonConstructorMissingMethods)) {
            echo "需要添加 'services_data' => \$services_data 的方法列表:\n\n";
            
            $currentFile = '';
            foreach ($this->nonConstructorMissingMethods as $item) {
                if ($currentFile !== $item['file']) {
                    $currentFile = $item['file'];
                    echo "📁 {$item['file']} ({$item['class']})\n";
                }
                echo "   ⚠️  {$item['method']}()\n";
            }
            
            echo "\n";
            echo "建议的修复步骤:\n";
            echo "1. 逐个检查上述方法的返回值结构\n";
            echo "2. 在适当的位置添加 'services_data' => \$services_data\n";
            echo "3. 确保 \$services_data 变量在方法中已定义\n";
            echo "4. 测试修改后的方法是否正常工作\n\n";
            
            // 生成修复脚本模板
            $this->generateFixTemplate();
        } else {
            echo "✅ 所有非构造函数方法都已包含 'services_data' => \$services_data\n";
        }
    }
    
    private function generateFixTemplate()
    {
        $templateFile = 'fix_services_data_template.txt';
        $template = "# 修复 'services_data' => \$services_data 缺失的方法\n\n";
        
        $currentFile = '';
        foreach ($this->nonConstructorMissingMethods as $item) {
            if ($currentFile !== $item['file']) {
                $currentFile = $item['file'];
                $template .= "## 文件: {$item['file']} (类: {$item['class']})\n";
            }
            $template .= "- [ ] 修复方法: {$item['method']}()\n";
        }
        
        $template .= "\n# 修复说明\n";
        $template .= "1. 在每个方法的返回数组中添加 'services_data' => \$services_data\n";
        $template .= "2. 确保 \$services_data 变量已正确定义\n";
        $template .= "3. 检查方法逻辑是否需要调整\n";
        
        file_put_contents($templateFile, $template);
        echo "修复模板已生成: {$templateFile}\n";
    }
}

// 执行分析
$analyzer = new DetailedServicesAnalysis();
$analyzer->analyze();

echo "分析完成！\n";

?>
