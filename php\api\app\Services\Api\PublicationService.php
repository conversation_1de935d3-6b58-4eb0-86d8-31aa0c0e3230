<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\Publication;
use App\Models\Resource;
use App\Models\User;
use App\Services\Api\ReviewService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 作品发布服务
 * 第3B阶段：作品发布系统
 */
class PublicationService extends Service
{
    protected $reviewService;

    public function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    /**
     * 发布作品
     */
    public function publishWork(int $userId, array $publicationData): array
    {
        try {
            DB::beginTransaction();

            // 验证资源权限和状态
            $resource = Resource::where('id', $publicationData['resource_id'])
                ->where('user_id', $userId)
                ->where('status', Resource::STATUS_COMPLETED) // 只接受已完成的资源
                ->first();

            if (!$resource) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '资源不存在或未完成',
                    'data' => []
                ];
            }

            // 检查是否已发布
            $existingPublication = Publication::where('resource_id', $resource->id)
                ->whereIn('status', [Publication::STATUS_PUBLISHED, Publication::STATUS_PENDING_REVIEW])
                ->first();

            if ($existingPublication) {
                return [
                    'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                    'message' => '该资源已发布或正在审核中',
                    'data' => []
                ];
            }

            // 创建发布记录
            $publication = Publication::create([
                'user_id' => $userId,
                'resource_id' => $resource->id,
                'title' => $publicationData['title'],
                'description' => $publicationData['description'],
                'tags' => $publicationData['tags'],
                'category' => $publicationData['category'],
                'visibility' => $publicationData['visibility'],
                'allow_comments' => $publicationData['allow_comments'],
                'allow_download' => $publicationData['allow_download'],
                'status' => Publication::STATUS_PENDING_REVIEW,
                'review_status' => Publication::REVIEW_STATUS_PENDING,
                'metadata' => [
                    'resource_type' => $resource->resource_type,
                    'file_size' => $resource->file_size,
                    'created_by' => 'publication_service'
                ]
            ]);

            // 自动提交审核
            $reviewResult = $this->reviewService->submitReview($userId, [
                'publication_id' => $publication->id,
                'review_type' => 'auto'
            ]);

            DB::commit();

            Log::info('作品发布成功', [
                'publication_id' => $publication->id,
                'user_id' => $userId,
                'resource_id' => $resource->id,
                'title' => $publicationData['title']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '作品发布成功',
                'data' => [
                    'publication_id' => $publication->id,
                    'resource_id' => $resource->id,
                    'title' => $publication->title,
                    'status' => $publication->status,
                    'visibility' => $publication->visibility,
                    'published_at' => null,
                    'review_status' => $publication->review_status,
                    'view_count' => 0,
                    'like_count' => 0,
                    'download_count' => 0
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'resource_id' => $publicationData['resource_id'] ?? null,
                'title' => $publicationData['title'] ?? null,
                'visibility' => $publicationData['visibility'] ?? null,
            ];

            Log::error('作品发布失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '作品发布失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取发布状态
     */
    public function getPublicationStatus(int $publicationId, int $userId): array
    {
        try {
            $publication = Publication::where('id', $publicationId)
                ->where('user_id', $userId)
                ->with(['resource.currentVersion'])
                ->first();

            if (!$publication) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '发布记录不存在',
                    'data' => []
                ];
            }

            // 从资源版本获取审核信息
            $currentVersion = $publication->resource->currentVersion ?? null;
            $reviewNotes = $currentVersion ? $currentVersion->review_notes : null;

            $data = [
                'publication_id' => $publication->id,
                'status' => $publication->status,
                'review_status' => $publication->review_status,
                'review_message' => $reviewNotes,
                'published_at' => $publication->published_at ? $publication->published_at->format('Y-m-d H:i:s') : null,
                'view_count' => $publication->view_count,
                'like_count' => $publication->like_count,
                'comment_count' => $publication->comment_count,
                'download_count' => $publication->download_count,
                'share_count' => $publication->share_count,
                'visibility' => $publication->visibility,
                'featured' => $publication->featured
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'publication_id' => $publicationId,
                'user_id' => $userId,
            ];

            Log::error('获取发布状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取发布状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新发布信息
     */
    public function updatePublication(int $publicationId, int $userId, array $updateData): array
    {
        try {
            $publication = Publication::where('id', $publicationId)
                ->where('user_id', $userId)
                ->first();

            if (!$publication) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '发布记录不存在',
                    'data' => []
                ];
            }

            // 如果作品正在审核中，某些字段不能修改
            if ($publication->status === Publication::STATUS_PENDING_REVIEW) {
                $restrictedFields = ['title', 'description', 'category'];
                foreach ($restrictedFields as $field) {
                    if (isset($updateData[$field])) {
                        return [
                            'code' => ApiCodeEnum::INVALID_OPERATION,
                            'message' => '作品审核中，不能修改核心信息',
                            'data' => []
                        ];
                    }
                }
            }

            $publication->update($updateData);

            Log::info('发布信息更新成功', [
                'publication_id' => $publicationId,
                'user_id' => $userId,
                'updated_fields' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '作品信息更新成功',
                'data' => [
                    'publication_id' => $publication->id,
                    'title' => $publication->title,
                    'description' => $publication->description,
                    'tags' => $publication->tags,
                    'visibility' => $publication->visibility,
                    'updated_at' => $publication->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $services_data = [
                'publication_id' => $publicationId,
                'user_id' => $userId,
                'update_data' => $updateData,
            ];

            Log::error('更新发布信息失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新发布信息失败',
                'data' => null
            ];
        }
    }

    /**
     * 取消发布
     */
    public function unpublishWork(int $publicationId, int $userId): array
    {
        try {
            $publication = Publication::where('id', $publicationId)
                ->where('user_id', $userId)
                ->first();

            if (!$publication) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '发布记录不存在',
                    'data' => []
                ];
            }

            $publication->update([
                'status' => Publication::STATUS_UNPUBLISHED,
                'unpublished_at' => Carbon::now()
            ]);

            // 清除相关缓存
            $this->clearPublicationCache($publication);

            Log::info('作品取消发布成功', [
                'publication_id' => $publicationId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '作品取消发布成功',
                'data' => [
                    'publication_id' => $publication->id,
                    'status' => $publication->status,
                    'unpublished_at' => $publication->unpublished_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $services_data = [
                'publication_id' => $publicationId,
                'user_id' => $userId,
            ];

            Log::error('取消发布失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '取消发布失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户发布列表
     */
    public function getUserPublications(int $userId, array $filters): array
    {
        try {
            $query = Publication::where('user_id', $userId);

            // 应用过滤条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $publications = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $publicationList = [];
            foreach ($publications->items() as $publication) {
                $publicationList[] = [
                    'publication_id' => $publication->id,
                    'title' => $publication->title,
                    'category' => $publication->category,
                    'status' => $publication->status,
                    'view_count' => $publication->view_count,
                    'like_count' => $publication->like_count,
                    'published_at' => $publication->published_at ? $publication->published_at->format('Y-m-d H:i:s') : null
                ];
            }

            // 计算统计信息
            $statistics = $this->calculateUserStatistics($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'publications' => $publicationList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $publications->currentPage(),
                        'per_page' => $publications->perPage(),
                        'total' => $publications->total(),
                        'last_page' => $publications->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'filters' => $filters,
            ];

            Log::error('获取用户发布列表失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取发布列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取作品广场
     */
    public function getPublicationPlaza(array $filters): array
    {
        try {
            $cacheKey = 'publication_plaza_' . md5(serialize($filters));
            
            return Cache::remember($cacheKey, 300, function () use ($filters) {
                $query = Publication::where('status', Publication::STATUS_PUBLISHED)
                    ->where('visibility', 'public')
                    ->with(['user:id,username,avatar']);

                // 应用过滤条件
                if (!empty($filters['category'])) {
                    $query->where('category', $filters['category']);
                }

                if (!empty($filters['tags'])) {
                    $tags = explode(',', $filters['tags']);
                    $query->where(function ($q) use ($tags) {
                        foreach ($tags as $tag) {
                            $q->orWhereJsonContains('tags', trim($tag));
                        }
                    });
                }

                if (!empty($filters['search'])) {
                    $search = $filters['search'];
                    $query->where(function ($q) use ($search) {
                        $q->where('title', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%");
                    });
                }

                // 排序
                switch ($filters['sort']) {
                    case 'popular':
                        $query->orderBy('view_count', 'desc');
                        break;
                    case 'trending':
                        $query->where('published_at', '>=', Carbon::now()->subDays(7))
                              ->orderByRaw('(view_count + like_count * 2) DESC');
                        break;
                    case 'featured':
                        $query->where('featured', true)->orderBy('published_at', 'desc');
                        break;
                    default:
                        $query->orderBy('published_at', 'desc');
                }

                // 分页
                $perPage = $filters['per_page'] ?? 20;
                $page = $filters['page'] ?? 1;

                $publications = $query->paginate($perPage, ['*'], 'page', $page);

                $publicationList = [];
                foreach ($publications->items() as $publication) {
                    $publicationList[] = [
                        'publication_id' => $publication->id,
                        'title' => $publication->title,
                        'description' => $publication->description,
                        'category' => $publication->category,
                        'tags' => $publication->tags,
                        'author' => [
                            'user_id' => $publication->user->id,
                            'username' => $publication->user->username,
                            'avatar' => $publication->user->avatar
                        ],
                        'view_count' => $publication->view_count,
                        'like_count' => $publication->like_count,
                        'comment_count' => $publication->comment_count,
                        'published_at' => $publication->published_at->format('Y-m-d H:i:s'),
                        'featured' => $publication->featured,
                        'thumbnail' => $publication->thumbnail
                    ];
                }

                // 获取精选作品
                $featuredWorks = $this->getFeaturedWorks();

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'publications' => $publicationList,
                        'featured_works' => $featuredWorks,
                        'pagination' => [
                            'current_page' => $publications->currentPage(),
                            'per_page' => $publications->perPage(),
                            'total' => $publications->total(),
                            'last_page' => $publications->lastPage()
                        ]
                    ]
                ];
            });

        } catch (\Exception $e) {
            $services_data = [
                'filters' => $filters,
            ];

            Log::error('获取作品广场失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取作品广场失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取作品详情
     */
    public function getPublicationDetail(int $publicationId, ?int $userId = null): array
    {
        try {
            $publication = Publication::where('id', $publicationId)
                ->where('status', Publication::STATUS_PUBLISHED)
                ->where('visibility', 'public')
                ->with(['user:id,username,avatar,follower_count', 'resource'])
                ->first();

            if (!$publication) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '作品不存在或不可访问',
                    'data' => []
                ];
            }

            // 增加浏览量
            $publication->increment('view_count');

            // 获取用户交互状态
            $userInteraction = [
                'liked' => false,
                'bookmarked' => false,
                'downloaded' => false
            ];

            if ($userId) {
                // 这里可以查询用户的点赞、收藏、下载记录
                // 简化实现，实际项目中需要相应的模型和关联
            }

            $data = [
                'publication_id' => $publication->id,
                'title' => $publication->title,
                'description' => $publication->description,
                'category' => $publication->category,
                'tags' => $publication->tags,
                'author' => [
                    'user_id' => $publication->user->id,
                    'username' => $publication->user->username,
                    'avatar' => $publication->user->avatar,
                    'follower_count' => $publication->user->follower_count ?? 0
                ],
                'resource_info' => [
                    'resource_id' => $publication->resource->id,
                    'resource_type' => $publication->resource->resource_type,
                    'file_size' => $publication->resource->formatted_file_size,
                    'duration' => null, // 如果是音频/视频，可以添加时长
                    'preview_url' => $publication->resource->file_path ? asset('storage/' . $publication->resource->file_path) : null
                ],
                'statistics' => [
                    'view_count' => $publication->view_count,
                    'like_count' => $publication->like_count,
                    'comment_count' => $publication->comment_count,
                    'download_count' => $publication->download_count,
                    'share_count' => $publication->share_count
                ],
                'published_at' => $publication->published_at->format('Y-m-d H:i:s'),
                'featured' => $publication->featured,
                'allow_comments' => $publication->allow_comments,
                'allow_download' => $publication->allow_download,
                'user_interaction' => $userInteraction
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'publication_id' => $publicationId,
                'user_id' => $userId,
            ];

            Log::error('获取作品详情失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取作品详情失败',
                'data' => null
            ];
        }
    }

    // 私有辅助方法
    private function calculateUserStatistics(int $userId): array
    {
        $publications = Publication::where('user_id', $userId)->get();

        return [
            'total_publications' => $publications->count(),
            'published_count' => $publications->where('status', Publication::STATUS_PUBLISHED)->count(),
            'pending_count' => $publications->where('status', Publication::STATUS_PENDING_REVIEW)->count(),
            'rejected_count' => $publications->where('review_status', Publication::REVIEW_STATUS_REJECTED)->count(),
            'total_views' => $publications->sum('view_count'),
            'total_likes' => $publications->sum('like_count')
        ];
    }

    private function getFeaturedWorks(): array
    {
        $featured = Publication::where('status', Publication::STATUS_PUBLISHED)
            ->where('featured', true)
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        $featuredList = [];
        foreach ($featured as $publication) {
            $featuredList[] = [
                'publication_id' => $publication->id,
                'title' => $publication->title,
                'category' => $publication->category,
                'view_count' => $publication->view_count,
                'like_count' => $publication->like_count
            ];
        }

        return $featuredList;
    }

    private function clearPublicationCache(Publication $publication): void
    {
        // 清除相关缓存
        Cache::forget('publication_plaza_*');
        Cache::forget('featured_works');
        Cache::forget('publication_detail_' . $publication->id);
    }
}
