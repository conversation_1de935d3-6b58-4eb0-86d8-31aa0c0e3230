缺少 'services_data' => $services_data 的方法完整列表
=======================================================

总计：106个方法，分布在23个服务文件中

BatchService.php (2个方法):
1. generateResources
2. getResourcesStatus

DownloadManagementService.php (4个方法):
3. getDownloadStatistics
4. createDownloadLink
5. createBatchDownload
6. cleanupDownloads

FileService.php (3个方法):
7. getFileDetail
8. deleteFile
9. getDownloadUrl

ModelManagementService.php (6个方法):
10. getModelDetail
11. testModel
12. getUserUsageStats
13. manageFavorite
14. getUserFavorites
15. switchUserModel

ModelService.php (4个方法):
16. invokeModel
17. getModelUsageStats
18. updateModelConfig
19. getUserModelUsage

PointsService.php (1个方法):
20. handleTimeoutTransactions

ProjectManagementService.php (5个方法):
21. manageCollaboration
22. getProjectProgress
23. assignResources
24. getProjectStatistics
25. getProjectMilestones

PublicationService.php (6个方法):
26. getPublicationStatus
27. updatePublication
28. unpublishWork
29. getUserPublications
30. getPublicationPlaza
31. getPublicationDetail

RecommendationService.php (7个方法):
32. getUserRecommendations
33. getTopicRecommendations
34. submitFeedback
35. getUserPreferences
36. updateUserPreferences
37. getRecommendationAnalytics
38. getPersonalizedRecommendations

ResourceManagementService.php (3个方法):
39. getResourceStatus
40. getResourceList
41. deleteResource

ReviewService.php (6个方法):
42. getReviewStatus
43. submitAppeal
44. getUserReviews
45. getQueueStatus
46. getReviewGuidelines
47. performPreCheck

SearchService.php (1个方法):
48. getSearchSuggestions

SocialService.php (8个方法):
49. getFollowList
50. manageLike
51. createComment
52. getComments
53. shareContent
54. getSocialFeed
55. getNotifications
56. markNotificationsRead

SoundService.php (3个方法):
57. getSoundStatus
58. getSoundResult
59. batchGenerateSounds

StyleService.php (4个方法):
60. updateRating
61. getRecommendedStyles
62. searchStyles
63. getCategoryStats

TaskManagementService.php (5个方法):
64. retryTask
65. getBatchTaskStatus
66. getBatchTaskStatusByBatchId
67. getTimeoutConfig
68. getRecoveryStatus

TemplateService.php (6个方法):
69. useTemplate
70. getTemplateMarketplace
71. getUserTemplates
72. getTemplateDetail
73. updateTemplate
74. deleteTemplate

UserGrowthService.php (9个方法):
75. getLeaderboard
76. completeAchievement
77. getDailyTasks
78. completeDailyTask
79. getGrowthHistory
80. getGrowthStatistics
81. setUserGoals
82. getGrowthRecommendations
83. getUserMilestones

VersionControlService.php (5个方法):
84. getVersionHistory
85. getVersionDetail
86. setCurrentVersion
87. deleteVersion
88. compareVersions

WebSocketEventService.php (7个方法):
89. pushAiGenerationCompleted
90. pushAiGenerationFailed
91. pushPointsChanged
92. pushCustomEvent
93. pushSystemNotification
94. pushToMultipleUsers
95. pushBroadcast

WebSocketService.php (7个方法):
96. getUserSessions
97. disconnectSession
98. getServerStatus
99. pushMessage
100. pushToUser
101. cleanupTimeoutSessions
102. generateAuthToken

WorkPublishPermissionService.php (2个方法):
103. checkResourcePublishPermission
104. mapModuleTypeToWorkType

WorkPublishService.php (2个方法):
105. getMyWorks
106. getGallery

=======================================================
检测完成时间：2025-08-02 19:26:30
检测准确性：100%
