{"check_time": "2025-08-02 19:26:30", "total_methods": 353, "missing_count": 106, "missing_methods": [{"file": "BatchService.php", "class": "BatchService", "method": "generateResources"}, {"file": "BatchService.php", "class": "BatchService", "method": "getResourcesStatus"}, {"file": "DownloadManagementService.php", "class": "DownloadManagementService", "method": "getDownloadStatistics"}, {"file": "DownloadManagementService.php", "class": "DownloadManagementService", "method": "createDownloadLink"}, {"file": "DownloadManagementService.php", "class": "DownloadManagementService", "method": "createBatchDownload"}, {"file": "DownloadManagementService.php", "class": "DownloadManagementService", "method": "cleanupDownloads"}, {"file": "FileService.php", "class": "FileService", "method": "getFileDetail"}, {"file": "FileService.php", "class": "FileService", "method": "deleteFile"}, {"file": "FileService.php", "class": "FileService", "method": "getDownloadUrl"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "getModelDetail"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "testModel"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "getUserUsageStats"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "manageFavorite"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "getUserFavorites"}, {"file": "ModelManagementService.php", "class": "ModelManagementService", "method": "switchUserModel"}, {"file": "ModelService.php", "class": "ModelService", "method": "invokeModel"}, {"file": "ModelService.php", "class": "ModelService", "method": "getModelUsageStats"}, {"file": "ModelService.php", "class": "ModelService", "method": "updateModelConfig"}, {"file": "ModelService.php", "class": "ModelService", "method": "getUserModelUsage"}, {"file": "PointsService.php", "class": "PointsService", "method": "handleTimeoutTransactions"}, {"file": "ProjectManagementService.php", "class": "ProjectManagementService", "method": "manageCollaboration"}, {"file": "ProjectManagementService.php", "class": "ProjectManagementService", "method": "getProjectProgress"}, {"file": "ProjectManagementService.php", "class": "ProjectManagementService", "method": "assignResources"}, {"file": "ProjectManagementService.php", "class": "ProjectManagementService", "method": "getProjectStatistics"}, {"file": "ProjectManagementService.php", "class": "ProjectManagementService", "method": "getProjectMilestones"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "getPublicationStatus"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "updatePublication"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "unpublishWork"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "getUserPublications"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "getPublicationPlaza"}, {"file": "PublicationService.php", "class": "PublicationService", "method": "getPublicationDetail"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "getUserRecommendations"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "getTopicRecommendations"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "submitFeedback"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "getUserPreferences"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "updateUserPreferences"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "getRecommendationAnalytics"}, {"file": "RecommendationService.php", "class": "RecommendationService", "method": "getPersonalizedRecommendations"}, {"file": "ResourceManagementService.php", "class": "ResourceManagementService", "method": "getResourceStatus"}, {"file": "ResourceManagementService.php", "class": "ResourceManagementService", "method": "getResourceList"}, {"file": "ResourceManagementService.php", "class": "ResourceManagementService", "method": "deleteResource"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "getReviewStatus"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "submitAppeal"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "getUserReviews"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "getQueueStatus"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "getReviewGuidelines"}, {"file": "ReviewService.php", "class": "ReviewService", "method": "perform<PERSON>reCheck"}, {"file": "SearchService.php", "class": "SearchService", "method": "getSearchSuggestions"}, {"file": "SocialService.php", "class": "SocialService", "method": "getFollowList"}, {"file": "SocialService.php", "class": "SocialService", "method": "manageLike"}, {"file": "SocialService.php", "class": "SocialService", "method": "createComment"}, {"file": "SocialService.php", "class": "SocialService", "method": "getComments"}, {"file": "SocialService.php", "class": "SocialService", "method": "shareContent"}, {"file": "SocialService.php", "class": "SocialService", "method": "getSocialFeed"}, {"file": "SocialService.php", "class": "SocialService", "method": "getNotifications"}, {"file": "SocialService.php", "class": "SocialService", "method": "markNotificationsRead"}, {"file": "SoundService.php", "class": "SoundService", "method": "getSoundStatus"}, {"file": "SoundService.php", "class": "SoundService", "method": "getSoundResult"}, {"file": "SoundService.php", "class": "SoundService", "method": "batchGenerateSounds"}, {"file": "StyleService.php", "class": "StyleService", "method": "updateRating"}, {"file": "StyleService.php", "class": "StyleService", "method": "getRecommendedStyles"}, {"file": "StyleService.php", "class": "StyleService", "method": "searchStyles"}, {"file": "StyleService.php", "class": "StyleService", "method": "getCategoryStats"}, {"file": "TaskManagementService.php", "class": "TaskManagementService", "method": "retryTask"}, {"file": "TaskManagementService.php", "class": "TaskManagementService", "method": "getBatchTaskStatus"}, {"file": "TaskManagementService.php", "class": "TaskManagementService", "method": "getBatchTaskStatusByBatchId"}, {"file": "TaskManagementService.php", "class": "TaskManagementService", "method": "getTimeoutConfig"}, {"file": "TaskManagementService.php", "class": "TaskManagementService", "method": "getRecoveryStatus"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "useTemplate"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "getTemplateMarketplace"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "getUserTemplates"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "getTemplateDetail"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "updateTemplate"}, {"file": "TemplateService.php", "class": "TemplateService", "method": "deleteTemplate"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getLeaderboard"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "completeAchievement"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getDailyTasks"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "completeDailyTask"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getGrowthHistory"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getGrowthStatistics"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "setUserGoals"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getGrowthRecommendations"}, {"file": "UserGrowthService.php", "class": "UserGrowthService", "method": "getUserMilestones"}, {"file": "VersionControlService.php", "class": "VersionControlService", "method": "getVersionHistory"}, {"file": "VersionControlService.php", "class": "VersionControlService", "method": "getVersionDetail"}, {"file": "VersionControlService.php", "class": "VersionControlService", "method": "setCurrentVersion"}, {"file": "VersionControlService.php", "class": "VersionControlService", "method": "deleteVersion"}, {"file": "VersionControlService.php", "class": "VersionControlService", "method": "compareVersions"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushAiGenerationCompleted"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushAiGenerationFailed"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushPointsChanged"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushCustomEvent"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushSystemNotification"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushToMultipleUsers"}, {"file": "WebSocketEventService.php", "class": "WebSocketEventService", "method": "pushBroadcast"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "getUserSessions"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "disconnectSession"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "getServerStatus"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "pushMessage"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "pushToUser"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "cleanupTimeoutSessions"}, {"file": "WebSocketService.php", "class": "WebSocketService", "method": "generateAuthToken"}, {"file": "WorkPublishPermissionService.php", "class": "WorkPublishPermissionService", "method": "checkResourcePublishPermission"}, {"file": "WorkPublishPermissionService.php", "class": "WorkPublishPermissionService", "method": "mapModuleTypeToWorkType"}, {"file": "WorkPublishService.php", "class": "WorkPublishService", "method": "getMyWorks"}, {"file": "WorkPublishService.php", "class": "WorkPublishService", "method": "getGallery"}]}