<?php

/**
 * 精确检测 Services/Api 目录中所有非构造函数的 public 方法
 * 确保100%准确性找出不包含 'services_data' => $services_data 的方法
 */

class PreciseServicesCheck
{
    private $servicesDir;
    private $missingMethods = [];
    private $allMethods = [];
    
    public function __construct()
    {
        $this->servicesDir = __DIR__ . '/php/api/app/Services/Api';
    }
    
    public function check()
    {
        echo "=== 精确检测非构造函数的 public 方法 ===\n\n";
        
        $files = glob($this->servicesDir . '/*.php');
        sort($files);
        
        foreach ($files as $file) {
            $this->checkFile($file);
        }
        
        $this->generateReport();
    }
    
    private function checkFile($filePath)
    {
        $fileName = basename($filePath);
        echo "检查文件: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "  ❌ 无法读取文件\n\n";
            return;
        }
        
        $className = $this->getClassName($content);
        
        // 使用更精确的正则表达式匹配 public 方法
        $pattern = '/^\s*public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/m';
        preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
        
        $publicMethods = [];
        $nonConstructorMethods = [];
        
        foreach ($matches[1] as $index => $methodMatch) {
            $methodName = $methodMatch[0];
            $methodStartPos = $matches[0][$index][1];
            
            $publicMethods[] = $methodName;
            
            // 跳过构造函数
            if ($methodName === '__construct') {
                echo "  ⏭️  跳过构造函数: {$methodName}\n";
                continue;
            }
            
            $nonConstructorMethods[] = $methodName;
            
            // 提取完整的方法体
            $methodBody = $this->extractCompleteMethodBody($content, $methodStartPos);
            
            // 记录方法信息
            $methodInfo = [
                'file' => $fileName,
                'class' => $className,
                'method' => $methodName,
                'body' => $methodBody
            ];
            
            $this->allMethods[] = $methodInfo;
            
            // 多种方式检查是否包含 services_data
            $hasServicesData = $this->checkServicesData($methodBody);
            
            if (!$hasServicesData) {
                $this->missingMethods[] = $methodInfo;
                echo "  ❌ {$methodName} - 缺少 'services_data' => \$services_data\n";
                
                // 显示方法体的一部分用于调试
                $preview = substr(str_replace(["\n", "\r"], ' ', $methodBody), 0, 200);
                echo "     预览: " . trim($preview) . "...\n";
            } else {
                echo "  ✅ {$methodName} - 包含 'services_data' => \$services_data\n";
            }
        }
        
        echo "  📊 总计: " . count($publicMethods) . " 个 public 方法, " . count($nonConstructorMethods) . " 个非构造函数\n\n";
    }
    
    private function getClassName($content)
    {
        if (preg_match('/^\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)/m', $content, $matches)) {
            return $matches[1];
        }
        return 'Unknown';
    }
    
    private function extractCompleteMethodBody($content, $startPos)
    {
        $lines = explode("\n", $content);
        $startLine = substr_count(substr($content, 0, $startPos), "\n");
        
        $braceCount = 0;
        $methodBody = '';
        $inMethod = false;
        
        for ($i = $startLine; $i < count($lines); $i++) {
            $line = $lines[$i];
            
            // 计算大括号
            for ($j = 0; $j < strlen($line); $j++) {
                $char = $line[$j];
                if ($char === '{') {
                    $braceCount++;
                    $inMethod = true;
                } elseif ($char === '}') {
                    $braceCount--;
                }
            }
            
            if ($inMethod) {
                $methodBody .= $line . "\n";
            }
            
            if ($inMethod && $braceCount === 0) {
                break;
            }
        }
        
        return $methodBody;
    }
    
    private function checkServicesData($methodBody)
    {
        // 多种检查方式确保准确性
        $patterns = [
            "'services_data' => \$services_data",
            '"services_data" => $services_data',
            "'services_data'=>\$services_data",
            '"services_data"=>$services_data',
            "'services_data' =>\$services_data",
            '"services_data" =>$services_data'
        ];
        
        foreach ($patterns as $pattern) {
            if (strpos($methodBody, $pattern) !== false) {
                return true;
            }
        }
        
        // 使用正则表达式进行更灵活的匹配
        $regexPattern = '/[\'"]services_data[\'"] *=> *\$services_data/';
        if (preg_match($regexPattern, $methodBody)) {
            return true;
        }
        
        return false;
    }
    
    private function generateReport()
    {
        echo "=== 最终检测报告 ===\n\n";
        
        $totalMethods = count($this->allMethods);
        $missingCount = count($this->missingMethods);
        $validCount = $totalMethods - $missingCount;
        
        echo "📊 统计结果:\n";
        echo "- 总非构造函数 public 方法数: {$totalMethods}\n";
        echo "- 包含 'services_data' => \$services_data: {$validCount}\n";
        echo "- 缺少 'services_data' => \$services_data: {$missingCount}\n\n";
        
        if ($missingCount > 0) {
            echo "❌ 缺少 'services_data' => \$services_data 的方法:\n\n";
            
            $currentFile = '';
            foreach ($this->missingMethods as $method) {
                if ($currentFile !== $method['file']) {
                    $currentFile = $method['file'];
                    echo "📁 {$method['file']} (类: {$method['class']})\n";
                }
                echo "   ⚠️  {$method['method']}()\n";
            }
            
            echo "\n";
            
            // 生成详细的缺失方法报告
            $this->generateDetailedMissingReport();
        } else {
            echo "✅ 所有非构造函数的 public 方法都包含 'services_data' => \$services_data\n";
        }
        
        // 保存完整报告
        $this->saveCompleteReport();
    }
    
    private function generateDetailedMissingReport()
    {
        $reportFile = 'missing_services_data_detailed.txt';
        $report = "缺少 'services_data' => \$services_data 的方法详细报告\n";
        $report .= "生成时间: " . date('Y-m-d H:i:s') . "\n";
        $report .= str_repeat("=", 60) . "\n\n";
        
        foreach ($this->missingMethods as $method) {
            $report .= "文件: {$method['file']}\n";
            $report .= "类名: {$method['class']}\n";
            $report .= "方法: {$method['method']}()\n";
            $report .= "方法体预览:\n";
            $report .= str_repeat("-", 40) . "\n";
            $report .= substr($method['body'], 0, 500) . "\n";
            $report .= str_repeat("-", 40) . "\n\n";
        }
        
        file_put_contents($reportFile, $report);
        echo "详细缺失报告已保存到: {$reportFile}\n";
    }
    
    private function saveCompleteReport()
    {
        $reportData = [
            'check_time' => date('Y-m-d H:i:s'),
            'total_methods' => count($this->allMethods),
            'missing_count' => count($this->missingMethods),
            'missing_methods' => array_map(function($method) {
                return [
                    'file' => $method['file'],
                    'class' => $method['class'],
                    'method' => $method['method']
                ];
            }, $this->missingMethods)
        ];
        
        $jsonFile = 'precise_check_report.json';
        file_put_contents($jsonFile, json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "完整报告已保存到: {$jsonFile}\n";
    }
}

// 执行精确检查
$checker = new PreciseServicesCheck();
$checker->check();

echo "\n精确检查完成！\n";

?>
